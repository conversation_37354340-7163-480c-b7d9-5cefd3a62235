import { inferResolvedUriFromRelativePath } from "../../util/ideUtils";

import { ToolImpl } from ".";
import { getCleanUriPath, getUriPathBasename } from "../../util/uri";
import { getOptionalStringArg, getStringArg } from "../parseArgs";
import { ToolExtras } from "../../index";

export const createNewFileImpl: ToolImpl = async (args, extras) => {
  const filepath = getStringArg(args, "filepath");
  const contents = getOptionalStringArg(args, "contents", true);
  const newCodeId = getOptionalStringArg(args, "newCodeId", true);

  const resolvedFileUri = await inferResolvedUriFromRelativePath(
    filepath,
    extras.ide,
  );
  if (resolvedFileUri) {
    const exists = await extras.ide.fileExists(resolvedFileUri);
    let contents_;
    if (newCodeId) {
      contents_ = await verifyCode(extras, { id: newCodeId });
      console.log(`verifyCode contents_: ${contents_}`);
    } else {
      contents_ = contents;
    }
    if (exists && contents_) {
      // throw new Error(
      //   `File ${filepath} already exists. Use the edit tool to edit this file`,
      // );
      return [
        {
          name: `File ${resolvedFileUri} already exists`,
          description:
            "File exists - consider whether to proceed with calling the 'get_proposed_changes' tool",
          content: `File ${resolvedFileUri} already exists, proceed to call the 'get_proposed_changes' tool to modify the existing file with the provided content: ${contents_}`,
          type: "error",
        },
      ];
    }
    await extras.ide.writeFile(resolvedFileUri, contents_ || "");
    await extras.ide.openFile(resolvedFileUri);
    await extras.ide.saveFile(resolvedFileUri);
    if (extras.codeBaseIndexer) {
      void extras.codeBaseIndexer?.refreshCodebaseIndexFiles([resolvedFileUri]);
    }
    return [
      {
        type: "success",
        name: getUriPathBasename(resolvedFileUri),
        description: getCleanUriPath(resolvedFileUri),
        content: "File created successfully",
        uri: {
          type: "file",
          value: resolvedFileUri,
        },
      },
    ];
  } else {
    // throw new Error("Failed to resolve path");
    return [
      {
        name: `Path Resolution Failed: "${resolvedFileUri}"`,
        description: "Unable to resolve the provided file path",
        content: `Failed to resolve path "${resolvedFileUri}". Please verify the path is correct and relative to the workspace root.`,
        type: "error",
      },
    ];
  }
};

async function verifyCode(
  extras: ToolExtras,
  query: any,
  options?: any,
): Promise<string> {
  const url = "https://aimi.alibaba-inc.com/ai/api/v1/agent/verify/code";
  const response = await extras.fetch(url, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(query),
    ...options,
    suffix: false,
  });
  return await response.text();
}
