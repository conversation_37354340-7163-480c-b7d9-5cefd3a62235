import { inferResolvedUriFromRelativePath } from "../../util/ideUtils";

import { ToolImpl } from ".";
import { getOptionalStringArg } from "../parseArgs";
import { ToolExtras } from "../../index";
import { logResponse } from "@continuedev/fetch/dist/fetch";

export const getProposedChangesImpl: ToolImpl = async (args, extras) => {
  const targetFile = getOptionalStringArg(args, "targetFile", true);
  const goal = getOptionalStringArg(args, "goal", true);
  const originalCode = getOptionalStringArg(args, "originalCode", true);
  const changedCodeId = getOptionalStringArg(args, "changedCodeId", true);

  if (!targetFile) {
    return [
      {
        name: "Proposed Changes Generation Failed",
        description: "Invalid target file path",
        content:
          "Please provide a valid relative file path for the target file",
        type: "error",
      },
    ];
  }

  if (!goal) {
    return [
      {
        name: "Proposed Changes Generation Failed",
        description: "Missing goal description",
        content:
          "Please provide a clear description of what you want to accomplish with this edit",
        type: "error",
      },
    ];
  }

  const normalizedTargetFile = targetFile.trim();

  const editContent = await editFile(extras, {
    originalCode,
    changedCodeId,
    goal,
    toolCallId: undefined,
  });
  console.log(`editFile editContent: ${editContent}`);
  if (editContent) {
    const resolvedFileUri = await inferResolvedUriFromRelativePath(
      normalizedTargetFile,
      extras.ide,
    );
    if (resolvedFileUri) {
      return [
        {
          name: "Proposed File Changes.",
          description: `Proposed changes for ${goal}.`,
          content: editContent,
          uri: {
            type: "file",
            value: resolvedFileUri,
          },
          type: "success",
        },
      ];
    } else {
      return [
        {
          name: `Path Resolution Failed: "${normalizedTargetFile}"`,
          description: "Unable to resolve the target file path",
          content: `Failed to resolve path "${normalizedTargetFile}". Please verify the path is correct and relative to the workspace root.`,
          type: "error",
        },
      ];
    }
  } else {
    return [
      {
        name: "Proposed Changes Generation Failed",
        description: `Failed to generate proposed changes for ${goal}.`,
        content: `Failed to generate proposed changes for goal: "${goal}". Please check your input parameters and try again.`,
        type: "error",
      },
    ];
  }
};

async function editFile(
  extras: ToolExtras,
  data: any,
  options?: any,
): Promise<String> {
  const url = "https://aimi.alibaba-inc.com/ai/api/v1/agent/edit/file";
  const response = await extras.fetch(url, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(data),
    ...options,
    suffix: false,
  });
  await logResponse(response);
  return await response.json()?.data;
}
