# Search Replace Editor Tool

基于 SWE-agent 的 str_replace_editor 实现的文件编辑工具。

## 功能特性

这个工具提供了类似于 SWE-agent 中 str_replace_editor 的功能，支持以下操作：

### 支持的命令

1. **view** - 查看文件或目录内容
2. **create** - 创建新文件
3. **str_replace** - 字符串替换
4. **insert** - 在指定行后插入内容
5. **undo_edit** - 撤销上一次编辑

### 使用示例

#### 查看文件
```json
{
  "command": "view",
  "path": "/workspace/example.py"
}
```

#### 查看文件的特定行范围
```json
{
  "command": "view",
  "path": "/workspace/example.py",
  "view_range": [10, 20]
}
```

#### 创建新文件
```json
{
  "command": "create",
  "path": "/workspace/new_file.py",
  "file_text": "print('Hello, World!')"
}
```

#### 字符串替换
```json
{
  "command": "str_replace",
  "path": "/workspace/example.py",
  "old_str": "def old_function():\n    pass",
  "new_str": "def new_function():\n    return True"
}
```

#### 插入内容
```json
{
  "command": "insert",
  "path": "/workspace/example.py",
  "insert_line": 5,
  "new_str": "# This is a new comment"
}
```

#### 撤销编辑
```json
{
  "command": "undo_edit",
  "path": "/workspace/example.py"
}
```

## 实现细节

### 文件结构

- `core/tools/definitions/searchReplaceEditor.ts` - 工具定义
- `core/tools/implementations/searchReplaceEditor.ts` - 工具实现

### 关键特性

1. **文件历史管理** - 每次编辑前自动保存文件状态，支持撤销操作
2. **错误处理** - 完善的错误检查和用户友好的错误消息
3. **路径解析** - 支持相对路径和绝对路径
4. **内容截断** - 长文件内容自动截断以节省上下文
5. **代码库索引刷新** - 编辑后自动刷新代码库索引

### 安全特性

1. **唯一性检查** - str_replace 操作要求 old_str 在文件中唯一出现
2. **文件存在检查** - 操作前验证文件是否存在
3. **参数验证** - 严格的参数类型和范围检查

## 与 SWE-agent 的兼容性

这个实现基于 SWE-agent 的 str_replace_editor 工具，保持了相同的接口和行为：

- 相同的命令参数结构
- 相同的错误处理逻辑
- 相同的输出格式
- 支持文件历史和撤销功能

## 集成说明

工具已经集成到系统中：

1. 在 `BuiltInToolNames` 中添加了 `SearchReplaceEditor`
2. 在 `getConfigDependentToolDefinitions` 中注册了工具
3. 在 `callBuiltInTool` 中添加了调用逻辑

## 注意事项

1. 文件历史存储在内存中，重启后会丢失
2. 大文件内容会被截断显示
3. str_replace 操作要求精确匹配，包括空格和换行符
4. 目录查看功能提供基本的文件列表
